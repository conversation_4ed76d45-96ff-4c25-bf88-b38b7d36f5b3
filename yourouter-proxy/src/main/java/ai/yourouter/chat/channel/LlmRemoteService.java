package ai.yourouter.chat.channel;

import ai.yourouter.chat.remote.KmgRemoteService;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.remote.BaseRemoteService;
import ai.yourouter.common.utils.GatewayHelperUtils;
import ai.yourouter.common.utils.JsonUtils;
import ai.yourouter.common.utils.TraceUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.util.HashMap;
import java.util.LinkedHashMap;

@Slf4j
public class LlmRemoteService extends BaseRemoteService {

    public LlmRemoteService(KmgRemoteService kmgRemoteService) {
        super(kmgRemoteService);
    }

    /**
     * 重写基类的错误日志记录方法，添加LLM特有的流式信息
     */
    @Override
    protected void loggingError(Throwable throwable, ChatContext context) {
        if (throwable instanceof WebClientResponseException ex) {
            log.error("请求错误 | 用户ID: {} | 模型: {} | 流式: {} | 状态码: {} | 响应体: {} | URL: {}",
                    context.getChatUserInfo().getCharactersId(),
                    context.apiModelName(),
                    context.getChatRequestStatistic().onStream(),
                    ex.getStatusCode(),
                    ex.getResponseBodyAsString(),
                    ex.getRequest() != null ? ex.getRequest().getURI() : null
            );
        } else {
            log.error("请求错误 | 用户ID: {} | 模型: {} | 流式: {} | 错误信息: {}",
                    context.getChatUserInfo().getCharactersId(),
                    context.apiModelName(),
                    context.getChatRequestStatistic().onStream(),
                    throwable.getMessage()
            );
        }
    }

    /**
     * 映射vendor名称，将内部vendor名称映射为对外显示的名称
     *
     * @param vendor 原始vendor名称
     * @return 映射后的vendor名称
     */
    protected static String mapVendorName(String vendor) {
        if ("ClaudeCode".equals(vendor)) {
            return "Claude";
        }
        return vendor;
    }

    /**
     * 处理流式响应数据，添加vendor和ID信息
     *
     * @param message 原始响应消息
     * @param chatContext 聊天上下文
     * @return 处理后的消息
     */
    protected static String processStreamWithVendorAndId(String message, ChatContext chatContext) {
        String mappedVendor = mapVendorName(chatContext.getKeyInfo().getChannel());
        return GatewayHelperUtils.handleSseLine(
                message,
                mappedVendor,
                chatContext.getRequestId()
        );
    }

    /**
     * 处理非流式响应数据，添加vendor和ID信息
     *
     * @param message 原始响应消息
     * @param chatContext 聊天上下文
     * @return 处理后的响应对象
     */
    protected Object processNonStreamWithVendorAndId(String message, ChatContext chatContext) {
        String mappedVendor = mapVendorName(chatContext.getKeyInfo().getChannel());
        String processedMessage = GatewayHelperUtils.injectVendorAndId(
                message,
                mappedVendor,
                TraceUtils.traceId()
        );
        return JsonUtils.parseObject(processedMessage, LinkedHashMap.class);
    }

    /**
     * 带调试信息的错误日志记录方法
     */
    protected void loggingErrorWithDebug(Throwable throwable, ChatContext context) {
        if (throwable instanceof WebClientResponseException ex) {
            log.error("请求错误 | 状态码: {} | 响应体: {} | URL: {} | 流式: {} | 模型: {} | 请求: {}",
                    ex.getStatusCode(),
                    ex.getResponseBodyAsString(),
                    ex.getRequest() != null ? ex.getRequest().getURI() : null,
                    context.getChatRequestStatistic().onStream(),
                    context.apiModelName(),
                    JsonUtils.toJSONString(context.getChatRequestStatistic().getRawRequest())
            );
        } else {
            log.error("请求错误 | 流式: {} | 模型: {} | 错误信息: {}",
                    context.getChatRequestStatistic().onStream(),
                    context.apiModelName(),
                    throwable.getMessage()
            );
        }
    }
}
